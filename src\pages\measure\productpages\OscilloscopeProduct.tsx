import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Check,
  Download,
  Phone,
  Zap,
  Monitor,
  Database,
  Wifi,
  Battery,
  Thermometer,
  ChevronDown,
  Gauge,
  Shield,
  BarChart
} from 'lucide-react';
import PageLayout from '@/components/layout/PageLayout';
import Carousel from '@/components/Carousel';

const OscilloscopeProduct = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  // Product list for dropdown
  const productList = [
    { id: 'handheld', model: 'OX 5022/OX 5042', subtitle: 'Handheld Digital Oscilloscope' },
    { id: 'portable', model: 'OX 9062/OX 9102/OX 9104/OX 9304', subtitle: 'Portable Digital Oscilloscope' }
  ];

  // Complete product data
  const productData = {
    handheld: {
      id: 'handheld',
      model: 'OX 5022/OX 5042',
      subtitle: 'Handheld Digital Oscilloscope',
      image: '/oscillosacopes old/OX 5042 , OX 5022.png',
      images: [
        '/oscillosacopes old/OX 5042 , OX 5022.png',
        '/oscillosacopes old/OX 9062-1.png',
        '/oscillosacopes old/OX 9062-2.png'
      ],
      voltage: 'CAT III 600V',
      measurement: 'Oscilloscope + Multimeter',
      accuracy: '20-40 MHz Bandwidth',
      price: 'Contact for pricing',
      description: 'Compact handheld oscilloscope with multimeter functions, designed for field measurements and troubleshooting applications with isolated channels for safety.',
      keyFeatures: [
        '3.5" colour TFT - Resolution 320 x 240',
        'LED backlighting',
        'Screen shot - Upto 100 files',
        'PC interface',
        '2 isolated channels',
        'Input impedance: 1 MΩ ±0.5%',
        '2 MB for file storage',
        '2,500 real acquisition points on screen'
      ],
      technicalSpecs: {
        'Model': 'OX 5022 (20 MHz) / OX 5042 (40 MHz)',
        'Bandwidth & Channels': '20 MHz & 2 isolated channels (OX 5022) / 40 MHz & 2 isolated channels (OX 5042)',
        'Input Impedance': '1 MΩ ±0.5%, approximately 17 pF',
        'File Storage': '2 MB for file storage',
        'Real Acquisition Points': '2,500 real acquisition points on screen',
        'Display': '3.5" colour TFT - Resolution 320 x 240',
        'Backlighting': 'LED backlighting',
        'Screen Shot': 'Upto 100 files',
        'Interface': 'PC interface',
        'Multimeter Mode': '2 channels, 8,000 count display + min/max bargraph',
        'Graphical Recording': '2,700 measurements (5 min to 1 month)',
        'AC & DC Voltages': '600 mV to 600 VRMS, 800 mV to 800 VDC',
        'Resistance & Capacitance': '80 Ω to 32 MΩ & 5 nF to 5 mF',
        'Frequency Measurement': 'Measures frequency, rotation speed, 3.3 V diode test, temperature'
      },
      applications: [
        'Field service and maintenance',
        'Automotive diagnostics',
        'Industrial troubleshooting',
        'Educational applications',
        'Power electronics testing',
        'Electrical installation testing'
      ],
      advantages: [
        'Portable handheld design',
        'Isolated channels for safety',
        'Multimeter functionality included',
        'High-resolution color display',
        'PC connectivity',
        'Extensive file storage capability'
      ]
    },
    portable: {
      id: 'portable',
      model: 'OX 9062/OX 9102/OX 9104/OX 9304',
      subtitle: 'Portable Digital Oscilloscope',
      image: '/oscilloscpoes ox 9104,9304/9J9A3371.JPG',
      images: [
        '/oscilloscpoes ox 9104,9304/9J9A3371.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3377.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3381.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3389.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3398.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3402.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3414.JPG',
        '/oscilloscpoes ox 9104,9304/9J9A3428.JPG'
      ],
      voltage: 'CAT II 300V',
      measurement: 'Advanced Oscilloscope',
      accuracy: '60-300 MHz Bandwidth',
      price: 'Contact for pricing',
      description: 'High-performance portable oscilloscope with advanced triggering, analysis capabilities and comprehensive measurement functions including harmonic analysis.',
      keyFeatures: [
        '7" WVGA colour TFT LCD touch screen, 800 x 480',
        'LED backlighting',
        '2,500 real acquisition points on screen',
        'PC interface',
        '2 or 4 isolated channels',
        'Input impedance: 1 MΩ ± 0.5%',
        '20 automatic measurements per channel',
        'Memory 2GB'
      ],
      technicalSpecs: {
        'Models': 'OX 9062 (60 MHz) / OX 9102 (100 MHz) / OX 9104 (100 MHz) / OX 9304 (300 MHz)',
        'Bandwidth': '60 MHz (OX 9062) / 100 MHz (OX 9102 & OX 9104) / 300 MHz (OX 9304)',
        'Channels': '2 isolated channels (OX 9062 & OX 9102) / 4 isolated Channels (OX 9104)',
        'Input Impedance': '1 MΩ ± 0.5%, approximately 12 pF',
        'Automatic Measurements': '20 automatic measurements per channel',
        'Memory': '2GB',
        'Display': '7" WVGA colour TFT LCD touch screen, 800 x 480 - LED backlighting',
        'Real Acquisition Points': '2,500 real acquisition points on screen',
        'Interface': 'PC interface',
        'Multimeter Mode': '2 or 4 channels - 8,000 counts min/max frequency/relative - TRMS',
        'AC & DC Voltages': '600 mV to 600 VRMS, 800 mV to 800 VDC',
        'Resistance & Capacitance': '80 Ω to 32 MΩ & 5 nF to 5 mF',
        'Frequency': '200 kHz, Rotation speed, 3.3 V diode test, temperature',
        'Harmonic Analyzer Mode': '2 or 4 (Based on model)',
        'Harmonics': 'Upto 63 orders',
        'Fundamental Frequency': '40 to 450 Hz in auto or manual mode',
        'Logger Mode': 'Duration: 20,000s - Interval: 0.2s - Files: 100,000 measurements'
      },
      applications: [
        'R&D and design verification',
        'Production testing',
        'Quality control',
        'Educational laboratories',
        'Service and repair',
        'Power quality analysis',
        'Harmonic analysis'
      ],
      advantages: [
        'Large touch screen display',
        'High bandwidth options up to 300 MHz',
        'Isolated channels for safety',
        'Comprehensive measurement capabilities',
        'Advanced harmonic analysis',
        'Extensive data logging',
        'Multiple connectivity options'
      ]
    }
  };

  const product = productData[productId as keyof typeof productData];

  useEffect(() => {
    if (!product) {
      navigate('/measure/oscilloscopes');
    } else {
      document.title = `${product.model} - ${product.subtitle} | Oscilloscope`;
    }
  }, [product, navigate]);

  if (!product) {
    return <div>Product not found</div>;
  }

  // Feature icon logic similar to MultimeterProduct
  const FeatureIcon = ({ feature }: { feature: string }) => {
    if (feature.toLowerCase().includes('display') || feature.toLowerCase().includes('lcd') || feature.toLowerCase().includes('tft') || feature.toLowerCase().includes('screen')) return <Monitor className="h-5 w-5" />;
    if (feature.toLowerCase().includes('memory') || feature.toLowerCase().includes('storage') || feature.toLowerCase().includes('data')) return <Database className="h-5 w-5" />;
    if (feature.toLowerCase().includes('connectivity') || feature.toLowerCase().includes('usb') || feature.toLowerCase().includes('ethernet') || feature.toLowerCase().includes('wifi')) return <Wifi className="h-5 w-5" />;
    if (feature.toLowerCase().includes('battery') || feature.toLowerCase().includes('power')) return <Battery className="h-5 w-5" />;
    if (feature.toLowerCase().includes('temperature') || feature.toLowerCase().includes('thermal')) return <Thermometer className="h-5 w-5" />;
    if (feature.toLowerCase().includes('voltage') || feature.toLowerCase().includes('current') || feature.toLowerCase().includes('channels')) return <Zap className="h-5 w-5" />;
    if (feature.toLowerCase().includes('bandwidth') || feature.toLowerCase().includes('sample') || feature.toLowerCase().includes('harmonics')) return <Gauge className="h-5 w-5" />;
    if (feature.toLowerCase().includes('safety') || feature.toLowerCase().includes('cat') || feature.toLowerCase().includes('isolated')) return <Shield className="h-5 w-5" />;
    if (feature.toLowerCase().includes('analysis') || feature.toLowerCase().includes('triggering') || feature.toLowerCase().includes('waveform')) return <BarChart className="h-5 w-5" />;
    return <Check className="h-5 w-5" />;
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      {/* Hide Breadcrumbs and Remove Top Padding */}
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>

      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Main Title Section */}
        <div className="py-8" style={{ background: '#F5C842' }}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Title always at top in mobile, center in desktop */}
            <div className="text-center mb-4 md:mb-0">
              <h1 className="typography-h1 text-black mb-2">
                Oscilloscopes
              </h1>
              <p className="typography-h4 text-black">
                Professional Signal Measurement Solutions
              </p>
            </div>
            {/* Responsive flex container for dropdown and back button */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full gap-4 md:gap-0">
              {/* Dropdown first on mobile, right on desktop */}
              <div
                className="order-1 md:order-2 w-full md:w-auto flex justify-center md:block"
                onMouseEnter={() => setDropdownOpen(true)}
                onMouseLeave={() => setDropdownOpen(false)}
              >
                <div className="relative w-full md:w-auto group">
                  <button
                    className="bg-white border border-yellow-400 text-black font-bold py-3 px-6 rounded-xl shadow-md flex items-center space-x-2 w-full md:w-auto justify-center md:justify-start transition-colors duration-200 focus:outline-none hover:bg-yellow-50"
                    style={{ fontWeight: 700, fontSize: '1.25rem' }}
                  >
                    <span>{product.model}</span>
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </button>
                  {dropdownOpen && (
                    <div className="absolute top-full left-0 right-0 md:right-auto md:w-80 mt-2 bg-white border border-yellow-400 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                      {productList.map((prod) => (
                        <button
                          key={prod.id}
                          onClick={() => navigate(`/measure/oscilloscopes/product/${prod.id}`)}
                          className={`w-full text-left px-4 py-3 hover:bg-yellow-50 transition-colors duration-200 border-b border-gray-100 last:border-b-0 ${
                            prod.id === product.id ? 'bg-yellow-50 font-bold' : ''
                          }`}
                        >
                          <div className="font-bold text-black">{prod.model}</div>
                          <div className="text-sm text-gray-600">{prod.subtitle}</div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Back button second on mobile, left on desktop */}
              <div className="order-2 md:order-1 w-full md:w-auto flex justify-center md:justify-start">
                <button
                  onClick={() => navigate('/measure/oscilloscopes')}
                  className="bg-white border border-yellow-400 text-black font-bold py-2 px-4 rounded-xl shadow-md hover:bg-yellow-50 transition-all duration-200 flex items-center space-x-2 w-full md:w-auto justify-center text-center"
                >
                  <span>&larr;</span>
                  <span>Back to Products</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Hero Section */}
        <div className="bg-gradient-to-br from-yellow-100 to-yellow-50 py-8 md:py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row justify-center md:justify-between items-center md:items-stretch gap-6 md:gap-8">
              {/* Content Left (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl order-2 md:order-1 flex flex-col justify-center">
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="space-y-4 order-1 lg:order-1"
                >
                  <div className="inline-block px-3 py-1.5 rounded-full text-black font-bold text-xs mb-3" style={{ backgroundColor: '#F5C842' }}>
                    {product.measurement}
                  </div>
                  <h1 className="text-3xl md:text-4xl font-bold text-black mb-3">
                    {product.model}
                  </h1>
                  <p className="text-lg text-yellow-700 font-semibold mb-4">
                    {product.subtitle}
                  </p>
                  <p className="text-base text-black leading-relaxed mb-6">
                    {product.description}
                  </p>
                  {/* Quick Specs */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Safety Rating</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.voltage}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md">
                      <h4 className="font-semibold text-black mb-1">Bandwidth</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.accuracy}</p>
                    </div>
                    <div className="bg-white p-3 rounded-xl shadow-md col-span-2">
                      <h4 className="font-semibold text-black mb-1">Price</h4>
                      <p className="font-bold" style={{ color: '#B8860B' }}>{product.price}</p>
                    </div>
                  </div>
                  {/* Action Buttons at Bottom */}
                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button onClick={() => navigate('/contact/sales')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Phone className="h-5 w-5" />
                      <span>Request Demo</span>
                    </button>
                    <button onClick={() => window.open('/T&M April 2025.pdf', '_blank')} className="flex-1 text-black font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2 text-sm hover:opacity-90" style={{ backgroundColor: '#F5C842' }}>
                      <Download className="h-5 w-5" />
                      <span>View Brochure</span>
                    </button>
                  </div>
                </motion.div>
              </div>
              {/* Image Carousel Right (on desktop) */}
              <div className="w-full md:w-1/2 max-w-2xl flex items-center justify-center order-1 md:order-2 mb-6 md:mb-0">
                <div className="w-full h-full bg-white rounded-2xl shadow-lg p-4" style={{ minHeight: '400px', minWidth: '400px' }}>
                  {product.images && product.images.length > 1 ? (
                    <Carousel
                      images={product.images}
                      className="w-full h-full"
                      theme="yellow"
                    />
                  ) : (
                    <img
                      src={product.image}
                      alt={product.model}
                      className="rounded-2xl shadow-lg object-contain w-full h-full bg-white"
                      style={{ background: '#fff', minHeight: '400px' }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features and Technical Specifications Section */}
        <div className="py-8 md:py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
              <div className="md:col-span-5">
                {/* Key Features Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="w-full bg-white rounded-2xl shadow-lg overflow-hidden p-8"
                >
                  <h2 className="text-3xl font-bold text-gray-900 mb-8">Key Features</h2>
                  <div className="space-y-4">
                    {product.keyFeatures.map((feature, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.05 }}
                        className="flex items-start gap-4 p-3 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                      >
                        <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                          <FeatureIcon feature={feature} />
                        </div>
                        <span className="text-gray-800 font-medium leading-relaxed">{feature}</span>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>

              {/* Technical Specifications Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="md:col-span-7 bg-white rounded-2xl shadow-lg overflow-hidden p-8"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Technical Specifications</h2>
                {/* Table Style Layout */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <tbody>
                      {Object.entries(product.technicalSpecs).map(([key, value], index) => (
                        <motion.tr
                          key={key}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: index * 0.05 }}
                          className={`border-b border-gray-100 hover:bg-yellow-50 transition-colors duration-200 ${
                            index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                          }`}
                        >
                          <td className="py-4 px-4 font-semibold text-gray-900 align-top w-1/3">
                            {key}
                          </td>
                          <td className="py-4 px-4 text-gray-700 font-medium">
                            {value}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Applications and Advantages Section */}
        <div className="py-8 md:py-12 bg-gradient-to-br from-yellow-50 to-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Applications */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Applications</h2>
                <div className="space-y-3">
                  {product.applications.map((application, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-2 h-2 bg-yellow-400 rounded-full flex-shrink-0"></div>
                      <span className="text-gray-700 font-medium">{application}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Advantages */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white rounded-2xl shadow-lg p-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Advantages</h2>
                <div className="space-y-3">
                  {product.advantages.map((advantage, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                      className="flex items-center gap-3 p-2 rounded-lg hover:bg-yellow-50 transition-colors duration-200"
                    >
                      <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <Check className="w-4 h-4 text-yellow-600" />
                      </div>
                      <span className="text-gray-700 font-medium">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Need Expert Advice?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Our specialists provide comprehensive guidance on oscilloscope solutions
              </p>
              <button
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
                onClick={() => navigate('/contact/sales')}
              >
                <span>Contact Sales</span>
                <Phone className="h-5 w-5" />
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default OscilloscopeProduct;
