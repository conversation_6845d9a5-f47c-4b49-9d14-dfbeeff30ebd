/* Wave movement keyframes and animation utilities */
@keyframes wave {
  0% { transform: translateX(0); }
  100% { transform: translateX(-33.333%); }
}

@keyframes wave-slow {
  0% { transform: translateX(0); }
  100% { transform: translateX(-20%); }
}

@keyframes wave-fast {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

@layer components {
  .animate-wave {
    animation: wave 12s linear infinite;
  }
  .animate-wave-slow {
    animation: wave-slow 20s linear infinite;
  }
  .animate-wave-fast {
    animation: wave-fast 8s linear infinite;
  }
}
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    /* Wave Design System */
    --wave-dark: 220 100% 85%;
    --wave-primary: 220 100% 50%;
    --wave-secondary: 220 90% 60%;
    --wave-light: 220 100% 70%;
    --wave-lightest: 220 50% 85%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.delay-300 {
  animation-delay: 0.3s;
}

/* Animations and Effects */
@layer components {
  .glass-panel {
    @apply bg-white/80 dark:bg-black/30 backdrop-blur-md border border-white/20 dark:border-white/10 shadow-glass;
  }

  .section-transition {
    @apply transition-all duration-700 ease-out;
  }

  .parallax-slow {
    transition: transform 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-medium {
    transition: transform 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .parallax-fast {
    transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-out hover:scale-105;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 3D Chatbot Button Animations */
  .chatbot-siri-pulse {
    animation: siri-pulse 2s ease-in-out infinite;
  }

  .chatbot-breathing {
    animation: breathing 3s ease-in-out infinite;
  }

  .chatbot-glow {
    animation: glow-pulse 2.5s ease-in-out infinite alternate;
  }
}

/* Keyframes for 3D Chatbot Animations */
@keyframes siri-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes breathing {
  0%,
  100% {
    transform: scale(1) translateZ(0);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.02) translateZ(2px);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.2), 0 0 60px rgba(59, 130, 246, 0.1);
  }
  100% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5),
      0 0 60px rgba(59, 130, 246, 0.3), 0 0 90px rgba(59, 130, 246, 0.2);
  }
}

@layer components {
  /* Standardized Typography System - Based on ChatGPT Values */
  /* Using Open Sans font family with consistent sizing and responsive design */

  /* Heading Styles */
  .typography-h1 {
    @apply text-3xl sm:text-4xl md:text-5xl font-bold leading-tight;
    font-family: 'Open Sans', sans-serif;
    font-weight: 700;
    line-height: 1.2;
  }

  .typography-h2 {
    @apply text-2xl sm:text-3xl md:text-4xl font-semibold leading-snug;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    line-height: 1.3;
  }

  .typography-h3 {
    @apply text-xl sm:text-2xl md:text-3xl font-semibold leading-snug;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    line-height: 1.35;
  }

  .typography-h4 {
    @apply text-lg sm:text-xl md:text-2xl font-medium leading-normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 500;
    line-height: 1.4;
  }

  .typography-h5 {
    @apply text-base sm:text-lg md:text-xl font-medium leading-normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 500;
    line-height: 1.5;
  }

  .typography-h6 {
    @apply text-sm sm:text-base md:text-lg font-medium leading-normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 500;
    line-height: 1.5;
  }

  /* Body Text Styles */
  .typography-body {
    @apply text-sm sm:text-base font-normal leading-relaxed;
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }

  .typography-small {
    @apply text-xs sm:text-sm text-gray-600 font-normal leading-normal;
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    line-height: 1.4;
  }

  .typography-extra-small {
    @apply text-xs text-gray-400 font-normal leading-snug;
    font-family: 'Open Sans', sans-serif;
    font-weight: 400;
    line-height: 1.3;
  }

  /* Legacy responsive classes - kept for backward compatibility */
  .text-responsive-xs {
    @apply typography-extra-small;
  }
  .text-responsive-sm {
    @apply typography-small;
  }
  .text-responsive-base {
    @apply typography-body;
  }
  .text-responsive-lg {
    @apply typography-h6;
  }
  .text-responsive-xl {
    @apply typography-h5;
  }
  .text-responsive-2xl {
    @apply typography-h4;
  }

  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  .container-tight {
    @apply w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  /* Improve button touch targets on mobile */
  button,
  a[role="button"],
  .btn,
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Force all sections to be visible */
  section, div[class*="section"], .relative {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ensure proper overflow handling */
  body, html {
    overflow-x: hidden !important;
  }

  /* Fix hero section specifically */
  .hero-section, [class*="hero"], [class*="carousel"] {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow: hidden !important;
  }

  /* Enhanced client logo section mobile support */
  .client-logos-section {
    -webkit-overflow-scrolling: touch !important;
    overflow-x: hidden !important;
    touch-action: pan-y !important;
  }

  /* Force hardware acceleration for smooth animations */
  .carousel-row, .carousel-track, .logo-item {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
  }

  /* Enhanced carousel navigation for mobile */
  .carousel-nav-button {
    min-width: 40px !important;
    min-height: 40px !important;
    font-size: 18px !important;
    z-index: 30 !important;
  }

  /* Chat button mobile enhancements */
  .chatbot-mobile {
    bottom: 16px !important;
    right: 16px !important;
    width: 64px !important;
    height: 64px !important;
    z-index: 60 !important;
  }

  /* Prevent navigation overlap */
  .navigation-safe-zone {
    margin-bottom: 20px !important;
    padding-bottom: 20px !important;
  }

  /* Optimize form inputs for mobile */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better spacing for mobile */
  .mobile-spacing {
    padding: 1rem;
  }

  /* Optimize images for mobile */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Fix text alignment issues on mobile */
  .text-justify {
    text-align: left;
  }

  /* Prevent horizontal overflow */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Better line height for readability - using typography system */
  p {
    @apply typography-body;
  }

  h1 {
    @apply typography-h1;
  }

  h2 {
    @apply typography-h2;
  }

  h3 {
    @apply typography-h3;
  }

  h4 {
    @apply typography-h4;
  }

  h5 {
    @apply typography-h5;
  }

  h6 {
    @apply typography-h6;
  }

  /* Ensure proper spacing between elements */
  .space-y-4 > * + * {
    margin-top: 1rem !important;
  }

  .space-y-6 > * + * {
    margin-top: 1.5rem !important;
  }

  /* Fix grid layouts on mobile */
  .grid {
    gap: 1rem !important;
  }

  /* Ensure flex items don't overflow */
  .flex {
    flex-wrap: wrap;
  }

  /* Better padding for containers */
  .container,
  .max-w-7xl,
  .max-w-6xl,
  .max-w-5xl {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Fix carousel height on mobile */
  .hero-carousel {
    min-height: 450px !important;
    max-height: 65vh !important;
  }

  /* Ensure banner images are properly sized */
  .banner-image {
    object-fit: cover !important;
    object-position: center !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Mobile-specific hero carousel fixes */
  .hero-carousel img {
    object-fit: cover !important;
    object-position: center !important;
    width: 100% !important;
    height: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
  }

  /* Ensure all sections are visible on mobile */
  .mobile-section {
    min-height: auto !important;
    padding: 1rem !important;
  }

  /* Fix stats grid on mobile */
  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.75rem !important;
  }

  /* Ensure proper text sizing on mobile */
  .mobile-text {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  /* Fix viewport issues on mobile browsers */
  .mobile-viewport {
    height: 100vh !important;
    height: 100dvh !important;
  }
}

/* Chatbot animations */
@keyframes chatbot-siri-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
}

@keyframes chatbot-breathing {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.chatbot-siri-pulse {
  animation: chatbot-siri-pulse 2s ease-in-out infinite;
}

.chatbot-breathing {
  animation: chatbot-breathing 3s ease-in-out infinite;
}

.chatbot-glow {
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
}

/* Tablet-specific optimizations */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet-specific styles */
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  /* Tablet chat button positioning */
  .chatbot-mobile {
    bottom: 24px !important;
    right: 24px !important;
    width: 56px !important;
    height: 56px !important;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  /* Desktop-specific styles */
  .desktop-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Additional responsive utilities for better mobile experience */
@layer utilities {
  /* Responsive text alignment */
  .text-responsive-center {
    @apply text-center sm:text-left;
  }

  .text-responsive-left {
    @apply text-left;
  }

  /* Typography utility classes for quick application */
  .heading-primary {
    @apply typography-h1;
  }

  .heading-secondary {
    @apply typography-h2;
  }

  .heading-tertiary {
    @apply typography-h3;
  }

  .text-content {
    @apply typography-body;
  }

  .text-caption {
    @apply typography-small;
  }

  .text-micro {
    @apply typography-extra-small;
  }

  /* Responsive spacing utilities */
  .spacing-responsive-sm {
    @apply space-y-2 sm:space-y-3 md:space-y-4;
  }

  .spacing-responsive-md {
    @apply space-y-3 sm:space-y-4 md:space-y-6;
  }

  .spacing-responsive-lg {
    @apply space-y-4 sm:space-y-6 md:space-y-8;
  }

  /* Responsive padding utilities */
  .padding-responsive-sm {
    @apply p-2 sm:p-3 md:p-4;
  }

  .padding-responsive-md {
    @apply p-3 sm:p-4 md:p-6;
  }

  .padding-responsive-lg {
    @apply p-4 sm:p-6 md:p-8;
  }

  /* Responsive margin utilities */
  .margin-responsive-sm {
    @apply m-2 sm:m-3 md:m-4;
  }

  .margin-responsive-md {
    @apply m-3 sm:m-4 md:m-6;
  }

  /* Responsive container utilities */
  .container-responsive {
    @apply w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20;
  }

  /* Responsive grid utilities */
  .grid-responsive-1-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-1-2-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-1-2-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4;
  }

  /* Responsive flex utilities */
  .flex-responsive-col {
    @apply flex flex-col sm:flex-row;
  }

  .flex-responsive-row {
    @apply flex flex-row;
  }

  /* Responsive width utilities */
  .width-responsive-full {
    @apply w-full sm:w-auto;
  }

  .width-responsive-auto {
    @apply w-auto;
  }

  /* Anti-overlap utilities */
  .no-overlap {
    position: relative;
    z-index: 1;
    overflow: hidden;
  }

  .safe-spacing {
    margin: 0.5rem;
    padding: 0.5rem;
  }

  /* Better mobile touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
  }

  /* Responsive image utilities */
  .img-responsive {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: cover;
  }

  .img-responsive-contain {
    width: 100%;
    height: auto;
    max-width: 100%;
    object-fit: contain;
  }

  /* Full container image coverage - no gaps */
  .img-full-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    min-width: 100%;
    min-height: 100%;
  }

  /* Enhanced image utilities for better visual prominence */
  .img-enhanced {
    filter: contrast(1.05) saturate(1.05) brightness(1.02);
    transition: filter 0.3s ease, transform 0.3s ease;
  }

  .img-enhanced:hover {
    filter: contrast(1.1) saturate(1.1) brightness(1.05);
  }

  /* Responsive viewport utilities */
  .min-h-screen-mobile {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .h-screen-mobile {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  /* Better responsive grid utilities */
  .grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  /* Responsive aspect ratio utilities */
  .aspect-video-responsive {
    aspect-ratio: 16/9;
  }

  .aspect-square-responsive {
    aspect-ratio: 1/1;
  }

  .aspect-photo-responsive {
    aspect-ratio: 4/3;
  }

  /* Better mobile scroll behavior */
  .scroll-smooth-mobile {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Responsive text truncation */
  .text-truncate-responsive {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .text-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Radial gradient utility for vignette effects */
  .bg-radial-gradient {
    background: radial-gradient(circle at center, var(--tw-gradient-stops));
  }

  /* Enhanced shadow utilities */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-4xl {
    box-shadow: 0 50px 100px -20px rgba(0, 0, 0, 0.25),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* Text wrapping utilities */
  .break-words {
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
  }

  /* Prevent text overflow */
  .text-ellipsis-responsive {
    @apply truncate sm:text-clip;
  }
}

.shimmer {
  @apply relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent;
}

/* Custom scrollbar styling for better visibility */
html, body {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #475569 #e2e8f0; /* Firefox - darker thumb, light track */
}

html::-webkit-scrollbar, body::-webkit-scrollbar {
  width: 14px; /* Chrome, Safari, Opera - slightly wider for better visibility */
}

html::-webkit-scrollbar-track, body::-webkit-scrollbar-track {
  background: #e2e8f0; /* Light gray track */
  border-radius: 7px;
}

html::-webkit-scrollbar-thumb, body::-webkit-scrollbar-thumb {
  background: #475569; /* Dark gray thumb for better visibility */
  border-radius: 7px;
  border: 2px solid #e2e8f0; /* Border to separate from track */
}

html::-webkit-scrollbar-thumb:hover, body::-webkit-scrollbar-thumb:hover {
  background: #334155; /* Even darker on hover */
}

/* Footer divider */
.footer-divider {
  @apply w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent;
}

/* Custom justified text without excessive spacing */
.text-justify-clean {
  text-align: justify;
  text-justify: inter-word;
  text-align-last: left;
  word-spacing: 0;
  letter-spacing: 0;
  hyphens: auto;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
}
